import pandas as pd

# 定义原始文件名和输出文件名
input_filename = '印度尼西亚_restaurant_urls_part3.csv'
output_filename_part1 = '印度尼西亚_restaurant_urls_part2.csv'
output_filename_part2 = '印度尼西亚_restaurant_urls_part3.csv'

try:
    # 读取CSV文件
    df = pd.read_csv(input_filename)

    # 计算总行数和分割点
    total_rows = len(df)
    split_point = total_rows // 2

    # 将DataFrame分割成两部分
    df_part1 = df.iloc[:split_point]
    df_part2 = df.iloc[split_point:]

    # 将两部分分别保存为新的CSV文件
    df_part1.to_csv(output_filename_part1, index=False, encoding='utf-8')
    df_part2.to_csv(output_filename_part2, index=False, encoding='utf-8')

    print("脚本执行成功！")
    print(f"原始文件 '{input_filename}' 共有 {total_rows} 行数据。")
    print(f"文件已成功拆分成两部分:")
    print(f" - 第一部分 '{output_filename_part1}' 包含 {len(df_part1)} 行。")
    print(f" - 第二部分 '{output_filename_part2}' 包含 {len(df_part2)} 行。")

except FileNotFoundError:
    print(f"错误：找不到文件 '{input_filename}'。请确保文件与脚本在同一目录下。")
except Exception as e:
    print(f"处理过程中发生错误: {e}")