import json
import pandas as pd
import requests
import os
import time

# --- 配置区 ---

# 警告：将API密钥硬编码在代码中存在严重安全风险。强烈建议使用环境变量。
# API_KEY = 'AIzaSyCy3eoW2ISRtmmUHuyJg8exRxhhqmn0CvI'  # <--- 您的 GEMINI_API_KEY

# 注意：请确认您有权限使用此模型。如果报错，请尝试 'gemini-2.5-flash-latest' 等有效模型。
# MODEL_NAME = 'gemini-2.5-flash' # 已根据您的注释修正为有效的模型
# API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/{MODEL_NAME}:generateContent?key={API_KEY}"
API_URL = 'http://113.98.195.218:1434/api/generate'
MODEL_NAME = 'qwen2.5-coder:32b'
# ==============================================================================
# --- 新增：模式选择 ---
# ==============================================================================
# 将此项设置为 True，可直接翻译下面的 MANUAL_BRANDS_INPUT 字符串，无需CSV文件。
# 将此项设置为 False，将执行原有的CSV批量翻译任务。
USE_DIRECT_STRING_INPUT = False

# 当 USE_DIRECT_STRING_INPUT 设置为 True 时，将翻译此处的品牌。
# 请将您需要翻译的品牌填入下方的字符串，并用'|'隔开。
MANUAL_BRANDS_INPUT = "The East - The Taste Of Indochine|Ivegan Supershop Old Quater Hanoi|Vinamilk|Shopee"
# ==============================================================================


# --- 文件和列配置 (当 USE_DIRECT_STRING_INPUT = False 时生效) ---
input_csv = 'all_shopeefood_restaurants.csv'
base_name, _ = os.path.splitext(input_csv)
output_csv = f"{base_name}_translated.csv"
brand_column = 'name'
translated_column = 'name_intl'  # 新的翻译列的名称

# 批处理参数
batch_size = 100
sleep_interval = 20 # 每批次休眠时间（秒）

# --- 提示词模板 (已修改输出分隔符) ---
prompt_template = """
你是一位顶级的品牌本地化专家和富有创造力的品牌命名大师，精通越南语、英语和中文，对中国文化和消费者的审美有深刻的洞察。你的任务是处理一个品牌名称列表，将列表中的每一个品牌都转化为一个完美的、符合中国人阅读习惯和审美、且极具商业吸引力的简体中文品牌名。

对于输入列表中的【每一个】品牌，你都必须遵循以下【进阶决策流程】：

1.  **【最高优先级】解析品牌结构与意境**：
    *   首先，分析品牌名是否包含“主品牌 + 描述/副标题/地名”的结构（例如 `The East - The Taste Of Indochine` 或 `Ivegan Supershop Old Quater Hanoi`）。
    *   **禁止**对描述或副标题进行生硬的字面翻译。你的任务是【提炼】描述部分的【核心意境】（如“Indochine” -> 异域风情），并将其【艺术性地融合】进主品牌的中文名中。
    *   对于地名，如果它是品牌身份的一部分（如指代特定分店），应以符合中文商业习惯的方式自然地体现出来，例如使用括号作为后缀 `（XX店）`。

2.  **核实官方名称**：
    *   在确定了需要翻译的核心品牌和其意境后，检索并优先使用其官方或市场公认的中文名称。

3.  **融合“信达雅”进行艺术再创**：
    *   如果不存在官方名称，你需要进行艺术性的再创造，务必达到以下标准：
    *   **信 (Faithfulness)**：忠实于品牌的核心定位和产品属性（例如，餐厅、商店）。
    *   **达 (Expressiveness)**：中文名必须流畅、自然，易于口头传播，不能有任何西式语法或翻译腔。
    *   **雅 (Elegance)**：这是【最重要的标准】。名称必须优美、有格调，能引发积极的联想，符合中国人的文化审美。选择寓意美好、韵律和谐的汉字。

4.  **最终输出格式要求**：
    *   处理完输入列表中的【所有】品牌后，将得到的中文名称按照与输入列表完全相同的顺序，以【竖线 |】分隔，形成一个单一的字符串。
    *   **绝对禁止**：不要添加任何编号、引号、解释、分析过程或任何与中文名称无关的字符。

---
**示例 1 (常规翻译)**
输入: `Vinamilk|Shopee`
期望输出: `维娜奶|虾皮`

---
**示例 2 (意境融合与地名处理)**
输入: `The East - The Taste Of Indochine|Ivegan Supershop Old Quater Hanoi`
期望输出: `东境越味|爱素家优选（河内老城店）`
---

输入品牌名称列表（用'|'分隔）：{brands}
"""


# --- API 调用函数（已修改响应解析方式） ---
def translate_batch(brands):
    non_empty_brands = [brand for brand in brands if pd.notna(brand) and str(brand).strip() != '']
    if not non_empty_brands:
        return ['' for _ in brands]

    brands_string = "|".join(non_empty_brands)
    prompt = prompt_template.format(brands=brands_string)

    payload = {
        "contents": [{"parts": [{"text": prompt}]}],
        "generationConfig": {"temperature": 0.2, "topP": 1.0},
        "safetySettings": [
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"},
        ]
    }
    headers = {'Content-Type': 'application/json'}

    try:
        response = requests.post(API_URL, headers=headers, json=payload, timeout=180)
        response.raise_for_status()
        response_json = response.json()
        output_text = response_json['candidates'][0]['content']['parts'][0]['text'].strip()
        # --- 【核心修改 2】: 使用'|'来分割API返回的响应字符串 ---
        translated_list = [name.strip() for name in output_text.split('|')]

        result = []
        translated_idx = 0
        for original_brand in brands:
            if pd.notna(original_brand) and str(original_brand).strip() != '':
                if translated_idx < len(translated_list):
                    result.append(translated_list[translated_idx])
                    translated_idx += 1
                else:
                    print(f"警告：API返回结果数量不足。源: '{original_brand}' 将被置空。")
                    result.append('')
            else:
                result.append('')
        return result
    except Exception as e:
        print(f"\n!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print(f"!!! 批次翻译失败: {e}")
        if 'response' in locals() and response is not None:
            print(f"--- 大模型返回的原始数据 ---")
            print(f"HTTP Status Code: {response.status_code}")
            try:
                pretty_response = json.dumps(response.json(), indent=2, ensure_ascii=False)
                print(f"Response Body (JSON):\n{pretty_response}")
            except json.JSONDecodeError:
                print(f"Response Body (Text):\n{response.text}")
            print(f"--- 数据结束 ---")
        else:
            print("--- 未能从API获取任何响应（可能是网络问题、超时或请求前置错误） ---")
        print(f"!!! 该批次所有翻译将置为空字符串，以保证数据对一对应。")
        print(f"!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n")
        return ['' for _ in brands]


# --- 主程序执行逻辑 (根据模式选择) ---
def run_direct_string_mode():
    """
    执行直接输入模式：翻译在脚本中指定的、用'|'分隔的品牌字符串。
    """
    print("--- 正在执行【直接输入模式】 ---")
    if not MANUAL_BRANDS_INPUT.strip():
        print("错误： MANUAL_BRANDS_INPUT 为空，请输入需要翻译的品牌。")
        return

    brands_list = [brand.strip() for brand in MANUAL_BRANDS_INPUT.split('|')]
    print(f"待翻译的品牌列表：{brands_list}")

    print("\n正在调用API进行翻译...")
    translated_names = translate_batch(brands_list)

    if len(translated_names) == len(brands_list):
        print("\n--- 翻译结果 ---")
        for original, translated in zip(brands_list, translated_names):
            print(f"原文: {original:<50} -> 译文: {translated}")
        print("--------------------")
    else:
        print("\n错误：翻译返回结果数量与输入不匹配。")


def run_csv_batch_mode():
    """
    执行CSV批量处理模式：读取CSV文件，进行批量翻译并保存结果。
    """
    print("--- 正在执行【CSV批量处理模式】 ---")
    if not os.path.exists(input_csv):
        print(f"错误：输入文件 '{input_csv}' 不存在。请检查文件名或将模式更改为直接输入。")
        return
        
    df = pd.read_csv(input_csv, header=0)
    total_rows = len(df)
    start_row = 0

    if os.path.exists(output_csv):
        print(f"检测到已存在的输出文件 '{output_csv}'。正在检查进度...")
        df_output = pd.read_csv(output_csv, header=0)
        if len(df_output) == total_rows and translated_column in df_output.columns:
            first_untranslated = df_output[translated_column].isnull().idxmax()
            if not df_output[translated_column].isnull().any():
                start_row = total_rows
            elif df_output.loc[first_untranslated, translated_column] == '' or pd.isna(
                    df_output.loc[first_untranslated, translated_column]):
                start_row = first_untranslated

            if start_row > 0:
                print(f"检测到 {start_row} 条已处理记录。将从第 {start_row + 1} 行开始继续。")
                df = df_output
            else:
                print("输出文件为空或格式不符，将从头开始翻译。")
                df[translated_column] = ''
        else:
            print(f"警告：'{output_csv}' 文件行数或列与源文件不匹配，将从头开始并覆盖。")
            df[translated_column] = ''
    else:
        print("未找到输出文件，将创建新文件并从头开始。")
        df[translated_column] = ''

    if translated_column in df.columns:
        cols = df.columns.tolist()
        if translated_column in cols:
            cols.remove(translated_column)
            if brand_column in cols:
                original_col_index = cols.index(brand_column)
                cols.insert(original_col_index + 1, translated_column)
                df = df[cols]

    if start_row >= total_rows:
        print("所有记录均已翻译。程序退出。")
    else:
        for i in range(start_row, total_rows, batch_size):
            end_index = min(i + batch_size, total_rows)
            batch = df[brand_column][i:end_index].tolist()

            if not batch:
                continue

            print(f"\n处理批次：记录 {i + 1} 到 {end_index}...")
            translated_batch = translate_batch(batch)

            if len(translated_batch) == len(batch):
                df.loc[i:end_index - 1, translated_column] = translated_batch
                try:
                    df.to_csv(output_csv, index=False, encoding='utf-8-sig')
                    print(f"批次完成。进度已保存到 '{output_csv}'")
                except Exception as e:
                    print(f"!!! 文件保存失败: {e}. 程序将终止。")
                    break
            else:
                print(f"错误：批次返回结果数量({len(translated_batch)})与批次数量({len(batch)})不符！该批次未保存。")

            if end_index < total_rows:
                print(f"休眠 {sleep_interval} 秒...")
                time.sleep(sleep_interval)

        print(f"\n翻译任务完成。最终输出已保存在 '{output_csv}'")


# --- 程序入口 ---
if __name__ == "__main__":
    if not API_KEY or 'AIzaSy' not in API_KEY: # 一个简单的检查，判断key是否已填
        raise ValueError("错误：请在脚本中设置您的 GEMINI_API_KEY。")

    if USE_DIRECT_STRING_INPUT:
        run_direct_string_mode()
    else:
        run_csv_batch_mode()